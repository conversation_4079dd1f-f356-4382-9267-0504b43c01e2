# RISC-V 内存管理相关指令整理

## 概述

RISC-V架构提供了一套完整的内存管理指令，主要用于虚拟内存管理、TLB操作和内存保护。这些指令主要在特权模式下使用，特别是在Supervisor模式和Machine模式中。

## 1. 内存管理栅栏指令 (Memory Management Fence Instructions)

### 1.1 SFENCE.VMA - Supervisor Fence Virtual Memory Address

**功能**: 同步虚拟内存地址转换的更新

**格式**: `SFENCE.VMA rs1, rs2`

**参数**:
- `rs1`: 虚拟地址 (可选，x0表示所有地址)
- `rs2`: 地址空间标识符ASID (可选，x0表示所有ASID)

**用途**:
- 刷新TLB (Translation Lookaside Buffer)
- 确保页表更新对后续内存访问可见
- 在修改页表项后必须执行

**示例**:
```assembly
sfence.vma x0, x0      # 刷新所有TLB条目
sfence.vma x5, x0      # 刷新特定虚拟地址的TLB条目
sfence.vma x0, x6      # 刷新特定ASID的所有TLB条目
sfence.vma x5, x6      # 刷新特定虚拟地址和ASID的TLB条目
```

### 1.2 HFENCE.VVMA - Hypervisor Fence Virtual Virtual Memory Address

**功能**: 虚拟化环境中的虚拟内存栅栏

**格式**: `HFENCE.VVMA rs1, rs2`

**用途**:
- 在虚拟化环境中刷新guest的虚拟地址转换
- 用于Hypervisor模式

### 1.3 HFENCE.GVMA - Hypervisor Fence Guest Virtual Memory Address

**功能**: 虚拟化环境中的guest物理地址栅栏

**格式**: `HFENCE.GVMA rs1, rs2`

**用途**:
- 刷新guest物理地址到host物理地址的转换
- 用于Hypervisor模式

## 2. 控制状态寄存器 (CSR) 相关指令

### 2.1 SATP (Supervisor Address Translation and Protection)

**CSR地址**: 0x180

**功能**: 控制S模式的地址转换和保护

**字段**:
- MODE: 分页模式 (Bare, Sv32, Sv39, Sv48, Sv57, Sv64)
- ASID: 地址空间标识符
- PPN: 页表基址的物理页号

**操作指令**:
```assembly
csrr t0, satp          # 读取satp
csrw satp, t1          # 写入satp
csrrs t0, satp, t1     # 读取并设置位
csrrc t0, satp, t1     # 读取并清除位
```

### 2.2 其他相关CSR

#### SSTATUS (Supervisor Status)
- **地址**: 0x100
- **功能**: S模式状态控制
- **重要字段**: SUM (Supervisor User Memory access), MXR (Make eXecutable Readable)

#### SCAUSE (Supervisor Cause)
- **地址**: 0x142
- **功能**: 记录异常/中断原因
- **内存相关异常**: 页错误、访问错误等

#### STVAL (Supervisor Trap Value)
- **地址**: 0x143
- **功能**: 记录异常相关的地址或指令

## 3. 内存访问权限控制

### 3.1 页表项权限位

RISC-V页表项包含以下权限位:
- **V**: Valid (有效位)
- **R**: Read (读权限)
- **W**: Write (写权限)
- **X**: Execute (执行权限)
- **U**: User (用户模式可访问)
- **G**: Global (全局映射)
- **A**: Accessed (已访问)
- **D**: Dirty (已修改)

### 3.2 内存保护单元 (PMP) 指令

#### PMPCFG - PMP Configuration
```assembly
csrr t0, pmpcfg0       # 读取PMP配置0
csrw pmpcfg0, t1       # 写入PMP配置0
```

#### PMPADDR - PMP Address
```assembly
csrr t0, pmpaddr0      # 读取PMP地址0
csrw pmpaddr0, t1      # 写入PMP地址0
```

## 4. 虚拟内存系统

### 4.1 支持的分页模式

| 模式 | 虚拟地址位数 | 物理地址位数 | 页表级数 |
|------|-------------|-------------|----------|
| Sv32 | 32          | 34          | 2        |
| Sv39 | 39          | 56          | 3        |
| Sv48 | 48          | 56          | 4        |
| Sv57 | 57          | 56          | 5        |

### 4.2 地址转换流程

1. 检查SATP.MODE确定分页模式
2. 从SATP.PPN获取页表基址
3. 逐级查找页表
4. 检查权限位
5. 返回物理地址或触发页错误

## 5. 常见使用场景

### 5.1 启用虚拟内存
```assembly
# 设置页表基址和模式
li t0, (SATP_MODE_SV39 << 60) | (page_table_ppn)
csrw satp, t0
sfence.vma x0, x0      # 刷新TLB
```

### 5.2 页表更新
```assembly
# 更新页表项
sw t0, 0(t1)           # 写入新的页表项
sfence.vma t2, x0      # 刷新对应虚拟地址的TLB
```

### 5.3 上下文切换
```assembly
# 切换地址空间
csrw satp, new_satp    # 加载新的页表
sfence.vma x0, x0      # 刷新所有TLB条目
```

## 6. 注意事项

1. **TLB一致性**: 修改页表后必须使用SFENCE.VMA确保TLB一致性
2. **权限检查**: 确保在正确的特权级别执行内存管理指令
3. **原子性**: 页表更新应该是原子的，避免竞态条件
4. **性能考虑**: 过度使用SFENCE.VMA会影响性能，应该精确指定刷新范围

## 7. 异常处理

### 7.1 内存相关异常类型

- **Instruction page fault** (12): 指令页错误
- **Load page fault** (13): 加载页错误  
- **Store/AMO page fault** (15): 存储/原子操作页错误
- **Instruction access fault** (1): 指令访问错误
- **Load access fault** (5): 加载访问错误
- **Store/AMO access fault** (7): 存储/原子操作访问错误

### 7.2 异常处理流程

1. 硬件设置SCAUSE和STVAL
2. 跳转到异常处理程序
3. 软件分析异常原因
4. 执行相应的处理 (如页面分配、权限检查等)
5. 返回或终止程序

这份整理涵盖了RISC-V架构中主要的内存管理相关指令和概念，可以作为学习和开发的参考资料。
